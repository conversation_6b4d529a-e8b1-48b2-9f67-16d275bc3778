#!/usr/bin/env python3
"""
Test script to verify the LicenseModel.workspaces fix
"""

import sys
import os

# Add the api directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'api'))

def test_license_workspaces_attribute():
    """Test that LicenseModel has workspaces attribute"""
    try:
        from services.feature_service import FeatureService
        
        # Get system features and check if license.workspaces exists
        system_features = FeatureService.get_system_features()
        
        print("Testing license.workspaces attribute...")
        
        # Check if workspaces attribute exists
        if hasattr(system_features.license, 'workspaces'):
            print("✓ license.workspaces attribute found")
            
            # Check if workspaces has is_available method
            if hasattr(system_features.license.workspaces, 'is_available'):
                print("✓ license.workspaces.is_available method found")
                
                # Test the is_available method
                result = system_features.license.workspaces.is_available()
                print(f"✓ license.workspaces.is_available() returned: {result}")
                return True
            else:
                print("✗ license.workspaces.is_available method NOT found")
                return False
        else:
            print("✗ license.workspaces attribute NOT found")
            return False
            
    except ImportError as e:
        print(f"Import error: {e}")
        return False
    except Exception as e:
        print(f"Error: {e}")
        return False

def test_account_service_import():
    """Test that account_service can be imported without errors"""
    try:
        from services.account_service import AccountService, TenantService, RegisterService
        print("✓ AccountService, TenantService, RegisterService imported successfully")
        return True
    except ImportError as e:
        print(f"Import error: {e}")
        return False
    except Exception as e:
        print(f"Error: {e}")
        return False

if __name__ == "__main__":
    print("Testing LicenseModel.workspaces fix...")
    print("=" * 50)
    
    test1_result = test_license_workspaces_attribute()
    test2_result = test_account_service_import()
    
    print("=" * 50)
    if test1_result and test2_result:
        print("✓ All tests passed! The license.workspaces fix should resolve the setup error.")
    else:
        print("✗ Some tests failed. The fix may need additional work.")
